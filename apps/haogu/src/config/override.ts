import dayjs from 'dayjs'
import { AsyncLock } from 'model/lock/lock'
import { defineOverride } from 'service/appkit/types'
import { startTasks } from 'service/visualized_sop/visualized_sop_task_starter'
import { calTaskTime } from '../visualized_sop/visualized_sop_processor'
import { chatStateStoreClient, chatDBClient } from './instance/base_instance'
import { commonMessageSender, eventTrackClient } from './instance/send_message_instance'
import { extractUserSlots } from './instance/instance'
import { ObjectUtil } from 'lib/object'
import { HumanTransferType, IChattingFlag, manifest } from './manifest'
import { ScrmOrderPlacedToAiStaff, ScrmWorkToAiStaff, ScrmCustomerEventToAiStaff, ScrmLinkReadMarkToAiStaff, ScrmMessageToAiStaff, ScrmReadMarkToAiStaff } from 'model/haogu/callback/type'
import logger from 'model/logger/logger'
import { PrismaMongoClient } from '../database/prisma'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { TaskName } from '../workflow/schedule_task/register_task'
import OpenAI from 'openai'
import axios from 'axios'
import { IWecomReceivedMsgType } from 'model/juzi/type'
import { IEventType } from 'model/logger/data_driven'
import { Config } from 'config'

const MAX_VIDEO_SIZE = 150 * 1024 * 1024 // 150MB
//TODO: 修改课程链接，链接是固定的
enum workLink {
  day1 = '1',
  day2 = '2',
  day3 = '3',
  day4 = '4',
  day5 = '5',
  day6 = '6',
}

/**
 * 对通用层注入函数
 */
export const override =  defineOverride({
  async onInit() {
    // 启动后钩子（可留空）
  },

  // 加好友后发送欢迎语
  async sendWelcomeMessage(chatId, userId) {
    const lock = new AsyncLock()
    await lock.acquire(chatId, async () => { // 如果有新消息，当前回复会被丢弃
      await chatStateStoreClient.update(chatId, {
        state: {
          is_friend_accepted: true
        }
      })

      const chat = await chatDBClient.getById(chatId)

      if (chat) {
        extractUserSlots.extractUserSlotsFromChatHistory({ chatId, chatHistory:[{
          role: 'user',
          date: dayjs().format('YYYY-MM-DD'),
          message: `我的名字是${chat.contact.wx_name}`
        }], topicRecommendations: extractUserSlots.getTopicRecommendations(), topicRules:extractUserSlots.getTopicRules() })
      }

      await startTasks(chatStateStoreClient, manifest.projectName, userId, chatId, calTaskTime)

      await commonMessageSender.sendText(chatId, {
        text: '😁您好呀，很开心认识您，我是您的《筹码主升训练营》的专属助教钱春艳。 \n👉接下来由我陪您学习，助您建立属于自己完整的交易体系! \n老师赠送的6节筹码课程在【8月17日（周天）】晚上19：20微信直播，记得置顶小钱微信 \n------------------- \n回复【666】免费领取筹码峰三大使用技巧 & 无延迟筹码工具',
        description: '欢迎语'
      })

      // 启动5分钟后的666后续消息定时任务
      await SilentReAsk.schedule(
        TaskName.send_666_follow_up,
        chatId,
        5 * 60 * 1000, // 5分钟
        undefined,
        { auto_retry: true, independent: false }
      )

    }, { timeout: 2 * 60 * 1000 })
  },

  async handleUnknownMessage(message) {

  },

  async handleImageMessage(imageUrl, chatId) {
    return `【图片Url】${imageUrl}`
  },


  async handleVideoMessage(videoUrl: string, chatId: string) {
    const dashscopeApiKey = Config.setting.qwen.apiKey || process.env.DASHSCOPE_API_KEY

    const openai = new OpenAI({
      apiKey: dashscopeApiKey,
      baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    })

    try {
      const sizeBytes = await getVideoFileSize(videoUrl)
      if (sizeBytes !== null && sizeBytes > MAX_VIDEO_SIZE) {
        throw new Error(`视频大小 ${ (sizeBytes / (1024 * 1024)).toFixed(2) } MB 超过 150 MB 限制`)
      }
      const messages: any = [
        {
          'role': 'user',
          'content': [{
            'type': 'video_url',
            'video_url': { 'url': videoUrl },
          },
          { 'type': 'text', 'text': '请以【视频】开头，然后分析视频的内容是什么，输出一段话，请不要使用markdown格式' }]
        }]
      const qwenResponse = await openai.chat.completions.create({
        model: 'qwen-omni-turbo',
        messages: messages,
        max_completion_tokens: 512,
        stream: true,
        stream_options: {
          include_usage: true
        },
        modalities: ['text']
      })
      let qwenResponseText = ''
      for await (const chunk of qwenResponse) {
        qwenResponseText += chunk.choices[0]?.delta.content || ''
      }
      qwenResponseText = qwenResponseText.trim()
      if (!qwenResponseText.startsWith('【视频】')) { qwenResponseText = `【视频】${qwenResponseText}` }
      qwenResponseText = qwenResponseText.replace(/\n/g, '')

      // await YuHeHumanTransfer.transfer(chatId, userId, YuHeHumanTransferType.ProcessVideo, 'onlyNotify', qwenResponseText)
      eventTrackClient.track(chatId, IEventType.TransferToManual, {
        reason: ObjectUtil.enumValueToKey(
          HumanTransferType,
          HumanTransferType.UnknownMessageType
        ),
        video_url: videoUrl,
        msg_type: ObjectUtil.enumValueToKey(
          IWecomReceivedMsgType,
          IWecomReceivedMsgType.Video
        ),
      })

      return qwenResponseText
    } catch (error) {
      logger.warn(`处理视频消息时出错: ${error}`)
      eventTrackClient.track(chatId, IEventType.TransferToManual, {
        reason: ObjectUtil.enumValueToKey(HumanTransferType, HumanTransferType.UnknownMessageType),
        video_url: videoUrl,
        msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, IWecomReceivedMsgType.Video),
      })
      return ''
    }
  },

  // 完成作业
  // TODO：处理不同分数的作业逻辑
  async handleFinishWork(data: { chat_id: string } & ScrmWorkToAiStaff) {
    if (data.link == workLink.day1) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day1:true
        }
      })
    } else if (data.link == workLink.day2) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day2:true
        }
      })
    } else if (data.link == workLink.day3) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day3:true
        }
      })
    } else if (data.link == workLink.day4) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day4:true
        }
      })
    } else if (data.link == workLink.day5) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day5:true
        }
      })
    } else if (data.link == workLink.day6) {
      await chatStateStoreClient.update(data.chat_id, {
        state:<IChattingFlag>{
          is_finish_homework_day6:true
        }
      })
    } else {
      logger.warn(`${data.chat_id}完成未知链接作业:${data.link}`)
    }

  },
  // 完成订单
  async handleOrder(data: {chat_id:string} & ScrmOrderPlacedToAiStaff) {
    await chatStateStoreClient.update(data.chat_id, {
      state:<IChattingFlag>{
        is_complete_payment:true
      }
    })
  },
  // 读消息
  async handleReadMessag(data:ScrmReadMarkToAiStaff) {
    const chatId = `${data.custUnifiedUserId}_${data.staffId}`
    const mongoClient = PrismaMongoClient.getInstance()
    await mongoClient.chat_history.updateMany({
      where:{
        chat_id:chatId
      },
      data:{
        is_read:true
      }
    })
  },
  //读链接
  async handleReadLink(data:ScrmLinkReadMarkToAiStaff) {

  },
  // 新客户
  async handleNewCustomer(data:ScrmCustomerEventToAiStaff) {
    if (data.status == 9 || data.status == 2057 || data.status == 2313) {
      //添加
      const chatId = `${data.custUnifiedUserId}_${data.staffId}`
      const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
      if (flags.is_friend_accepted) {
        logger.log(`已经添加了好友,chat_id:${chatId}`)
        return
      }
      const mongoClient = PrismaMongoClient.getInstance()
      await mongoClient.chat.upsert({ where:{ id:chatId }, create:{
        id:chatId,
        contact:{
          wx_id:data.custUnifiedUserId,
          wx_name:data.custNickname
        },
        wx_id:String(data.staffId),
        created_at:new Date(),
        chat_state:{
          nodeInvokeCount:{},
          nextStage:'free_talk',
          userSlots:{},
          state:{}
        },
        course_no:Number(dayjs().format('YYYYMMDD')),
        conversation_id:data.conversationId,
        customer_tool_user_id:data.custToolUserId,
        wx_union_id:data.custUnionId
      }, update:{
        is_deleted:false
      } })
      await this.sendWelcomeMessage(chatId, data.custUnifiedUserId)
    } else if (data.status == 0 || data.status == 8 || data.status == 2049) {
      //删除
      const chatId = `${data.custUnifiedUserId}_${data.staffId}`
      const mongoClient = PrismaMongoClient.getInstance()
      await mongoClient.chat.update({ where:{
        id:chatId
      }, data:{
        is_deleted:true
      } })
    } else {
      logger.error('新客户接口未知status', data)
    }
  }
})

async function getVideoFileSize(url: string) {
  try {
    const res = await axios.head(url, { timeout: 5000 })
    const length = res.headers['content-length']
    return length ? parseInt(length, 10) : null
  } catch (error) {
    logger.warn(`HEAD 请求失败，无法获取视频文件大小: ${error}`)
    return null
  }
}