import { IWorkflowState } from 'service/llm/state'
import { LLM } from 'lib/ai/llm/llm_model'
import logger from 'model/logger/logger'
import { Node, WorkFlowNode } from './node'
import { DateHelper } from 'lib/date/date'
import { DataService } from './helper/getter/get_data'
import { PrismaMongoClient } from '../database/prisma'
import { RegexHelper } from 'lib/regex/regex'
import { checkRobotDetection } from 'service/agent/utils'
import { getPrompt } from 'service/agent/prompt'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../config/instance/base_instance'
import { humanTransferClient } from '../config/instance/instance'
import { commonMessageSender, eventTrackClient, replyClient } from '../config/instance/send_message_instance'
import { ContextBuilder } from './context'
import { stageFilter } from './meta_action/stage'
import { FreeTalk } from 'service/agent/freetalk'
import { IChattingFlag } from '../config/manifest'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { TaskName } from './schedule_task/register_task'
import { sleep } from 'lib/schedule/schedule'

export class FreeTalkNode extends WorkFlowNode {
  public static async invoke(state: IWorkflowState) {
    const freeTalk = new FreeTalk(chatHistoryServiceClient, ContextBuilder, eventTrackClient, replyClient, stageFilter)
    return await freeTalk.invoke(state)
  }
}

export class Router {
  /**
   * 根据客户消息进行路由，特别注意这里的路由要写的 特定情况才能跳转，不能太通用，不然容易路由到错误的节点
   * 返回 End, 表示不执行任何节点逻辑
   * @param state
   */
  public static async route(state: IWorkflowState): Promise<Node> {
    const chatId = state.chat_id
    const userId = state.user_id
    const roundId = state.round_id
    const userMessage = state.userMessage
    const currentTime = await DataService.getCurrentTime(chatId)
    const mongoClient = PrismaMongoClient.getInstance()
    const day = currentTime.day
    if (!userMessage) return Node.Dummy

    // 刷新到完课状态
    if (0 < day && day < 5 && DateHelper.isTimeAfter(currentTime.time, '18:50:00')) {
      // await DataService.isAttendCourse(chatId, day)
      await DataService.isCompletedCourse(chatId, day)
    }

    // 废话过滤
    const isChatter = RegexHelper.filterChatter(userMessage)
    const beforeCourse3 = await DataService.isInCourseTimeLine(chatId, 'beforeCourse', 3)
    if (isChatter && beforeCourse3) return Node.DummyEnd

    // 客户识别AI检查
    const isRobotDetection = await checkRobotDetection(chatStateStoreClient, humanTransferClient, chatId, roundId, userId, userMessage)
    if (isRobotDetection) return Node.DummyEnd

    // 检测客户回复"666"
    const is666Reply = await this.check666Reply(userMessage, chatId)
    if (is666Reply) return Node.DummyEnd

    // 检测客户回复"888"
    const is888Reply = await this.check888reply(userMessage, chatId)
    if (is888Reply) return Node.DummyEnd

    // 意图分类路由
    return await this.routeByCategory(userMessage, chatId, userId, roundId)
  }

  static checkStockImage(userMessage: string): boolean {
    if (['不是抖音首页', '并非抖音首页', '【普通图片】'].some((item) => userMessage.includes(item))) {
      return false
    }
    return userMessage.includes('【社交媒体首页截图】')
  }

  static checkRewardAsking(userMessage: string): boolean {
    const KEYWORDS = ['完课礼', '定位', '指南', '爆单', '祝福', '好评礼']
    return userMessage.split('\n').some((subItem) => KEYWORDS.includes(subItem.trim()))
  }

  static checkHomework1(userMessage: string): boolean {
    return ['商业定位', '内容定位', '人设定位', '你想一个月通过抖音赚多少钱'].some((item) => userMessage.includes(item))
  }

  static checkHomework2(userMessage: string): boolean {
    return ['你的身份', '你打算做什么', '提出具体要求'].some((item) => userMessage.includes(item))
  }

  static async check666Reply(userMessage: string, chatId: string): Promise<boolean> {
    if (!userMessage.includes('666')) {
      return false
    }

    // 避免重复发送
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (state.is_send_666_follow_up_message) {
      return false
    }

    await sleep(8000)

    // 发送666后续消息
    await commonMessageSender.sendText(chatId, {
      text: '筹码峰核心资料： \n【神奇的双线合一】a.ckjr001.com/xq4d3d/pe9joj3 （约7分钟） \n【绝密的四点共振】a.ckjr001.com/0NkyNm/pe9joj3（约8分钟） \n筹码买点战法看完回小楚【888】，我来教您安装和设置 无延迟筹码峰 。六节体系课程明天19：20开始。',
      description: '666回复消息'
    })

    // 更新状态
    await chatStateStoreClient.update(chatId, {
      state: <IChattingFlag>{
        is_send_666_follow_up_message: true
      }
    })

    // 启动888后续消息定时任务（5分钟后）
    await SilentReAsk.schedule(
      TaskName.send_888_follow_up,
      chatId,
      5 * 60 * 1000, // 5分钟
      undefined,
      { auto_retry: true, independent: false }
    )

    return true
  }

  static async check888reply(userMessage: string, chatId: string): Promise<boolean> {
    if (!userMessage.includes('888')) {
      return false
    }
    // 避免重复发送
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (state.is_send_888_follow_up_message) {
      return false
    }

    await sleep(10000)

    // 发送888后续消息
    await commonMessageSender.sendText(chatId, {
      text: '手机直接在平时下载软件的地方搜索【好人好股】下载就可以了哦 这是手机如何调出筹码峰视频：https://ai.9635.com.cn/hZRMmBbJ 不明白随时来问我哦\n' +
          '​\n' +
          '下面是电脑如何调视频有什么不明白的和小钱说 【先看视频哦】电脑筹码安装视频：https://ai.9635.com.cn/2T9ykocA 【电脑版】下载链接： https://www.9635.com.cn/web/jinrong/share',
      description: '888回复消息'
    })

    await sleep(5000)

    await commonMessageSender.sendText(chatId, {
      text: '上面是安装方法，一定要看完视频才能调出无延迟筹码峰哦 调出筹码峰后告诉小钱，我教你如何利用筹码峰找卖点',
      description: '888回复消息'
    })

    // 更新状态
    await chatStateStoreClient.update(chatId, {
      state: <IChattingFlag>{
        is_send_888_follow_up_message: true
      }
    })

    return true
  }

  static checkPaymentImage(userMessage: string): boolean {
    return userMessage.includes('图片') &&
      ['2980', '2975'].some((amount) => userMessage.includes(amount)) &&
      ['支付', '交易', '成功', '完成'].some((result) => userMessage.includes(result)) &&
      // ['宇合传媒', '星河披星', '星河交辉', '星河星辰', '星河众星', '星河拱月', '获客盈利', '传媒', '中神通', '银河商学', '星河觉醒'].some((name) => userMessage.includes(name)) &&
      !['最高500元', '交易已超额', '请降低金额', '或下月再付', '当前商户存在异常', '暂时无法支付', '限额'].some((amount) => userMessage.includes(amount))
  }

  // 意图分类路由
  private static async routeByCategory(userMessage: string, chat_id: string, user_id: string, round_id: string): Promise<Node> {
    const category = await Router.classify(userMessage, chat_id, round_id)
    const beforeCourse1 = await DataService.isInCourseTimeLine(chat_id, 'beforeCourse', 1)
    const afterCourse1Sales = await DataService.isInCourseTimeLine(chat_id, 'afterSales', 1)
    const afterCourse2Sales = await DataService.isInCourseTimeLine(chat_id, 'afterSales', 2)

    if (category === 1 && afterCourse1Sales) {
      return Node.SendFile
    } else if (category === 2 && afterCourse1Sales) {
      return Node.Homework1
    } else if (category === 3 && afterCourse2Sales) {
      return Node.Homework2
    } else if (category === 4 && !beforeCourse1) {
      // await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.ProblemSolving, 'onlyNotify', `客户：${userMessage}`)
      const phoneNumber = await chatDBClient.getPhone(chat_id)
      const message = phoneNumber ? `用这个手机号登录哈，老师帮你开权限了\n${phoneNumber}` : '麻烦提供一下手机号哈，这边后台帮你开权限'
      await commonMessageSender.sendText(chat_id, {
        text: message,
        description: '客户无法看课，给解决方案'
      })
      return Node.DummyEnd
    } else if (category === 5) {
      // await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.ProblemSolving, 'onlyNotify', `客户：${userMessage}`)
    } else if (category === 6) {
      // await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.ProblemSolving, true, `客户：${userMessage}`)
      return Node.DummyEnd
    }
    return Node.Dummy
  }

  public static async classify(userMessage: string, chat_id: string, round_id: string) {
    const routerPrompt = await getPrompt('free-route')
    const routingNodes = `1. 发送资料：客户消息为索要完课礼/课程回放/好评礼/作业或其他课程相关资料时，判断前请不要进行过度推理
  - 例如：
  - 注意：

2. 曲线诊断：
  - 例如：
  - 注意：

4. 看课问题：客户表示无法进入直播，无权限观看，无法看录播等看课相关问题时进入此节点
  - 注意：网络问题，卡顿，陪跑群问题等跟看课无关问题不属于这个节点

5. 异常问题：客户表示投诉，辱骂，退款，无法下单/支付，支付截图，已经付款，延期上课，更换课程时间或想来公司现场看看等问题时进入此节点
  - 例如：“你们的产品是骗人的”“我要投诉”“我要退款/退费/退课”“我想上下一期课程”“付了”

6. 人工处理：客户消息仅为直播链接失效，付款限额，付款失败时进入此节点，其他特殊问题属于节点5`
    const output = await LLM.predict(
      routerPrompt, {
        responseJSON: true,
        meta: {
          promptName: 'router',
          chat_id: chat_id,
          round_id: round_id,
        } }, {
        routingNodes: routingNodes,
        customerMessage: userMessage,
      })
    let answer: number = 0

    try {
      const parsedOutput = JSON.parse(output)
      answer = parsedOutput.answer
    } catch (error) {
      logger.error('Router 解析 JSON 失败:', error)
    }
    return answer || 0
  }
}