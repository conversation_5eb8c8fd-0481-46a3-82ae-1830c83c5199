import { ActionInfo } from 'service/agent/stage'
import { Config } from 'config'
import { PromptTemplate } from '@langchain/core/prompts'
import { LLM } from 'lib/ai/llm/llm_model'
import { DataService } from '../helper/getter/get_data'

interface SendFileGuidance{
  shouldSend: boolean
  guidance: string
}

interface ISendFile{
  fileName: string
  fileId: string
  condition?: (chatId: string, roundId: string) => Promise<SendFileGuidance>
}


export class SendFileAction {


  public static fileList: ISendFile[] = [
    {
      fileName: '预习课总结',
      fileId: '303369',
    },
    {
      fileName: '第一课约课礼',
      fileId: '303369',
      condition: async (chatId: string, roundId: string) => {
        const currentTime = await DataService.getCurrentTime(chatId)

        if (currentTime.day < 1) {
          return {
            shouldSend: false,
            guidance: '课程预约还未开始'
          }
        }

        return {
          shouldSend: true,
          guidance: ''
        }
      },
    },
    {
      fileName: '第二课约课礼',
      fileId: '303369',
      condition: async (chatId: string, roundId: string) => {
        const currentTime = await DataService.getCurrentTime(chatId)

        if (currentTime.day < 2) {
          return {
            shouldSend: false,
            guidance: '课程预约还未开始'
          }
        }

        return {
          shouldSend: true,
          guidance: ''
        }
      },
    },
    {
      fileName: '第三课约课礼',
      fileId: '303369',
      condition: async (chatId: string, roundId: string) => {
        const currentTime = await DataService.getCurrentTime(chatId)

        if (currentTime.day < 3) {
          return {
            shouldSend: false,
            guidance: '课程预约还未开始'
          }
        }

        return {
          shouldSend: true,
          guidance: ''
        }
      },
    },
    {
      fileName: '第四课约课礼',
      fileId: '303369',
      condition: async (chatId: string, roundId: string) => {
        const currentTime = await DataService.getCurrentTime(chatId)

        if (currentTime.day < 4) {
          return {
            shouldSend: false,
            guidance: '课程预约还未开始'
          }
        }

        return {
          shouldSend: true,
          guidance: ''
        }
      },
    },
    {
      fileName: '第五课约课礼',
      fileId: '303369',
      condition: async (chatId: string, roundId: string) => {
        const currentTime = await DataService.getCurrentTime(chatId)

        if (currentTime.day < 5) {
          return {
            shouldSend: false,
            guidance: '课程预约还未开始'
          }
        }

        return {
          shouldSend: true,
          guidance: ''
        }
      },
    },
    {
      fileName: '第六课约课礼',
      fileId: '303369',
      condition: async (chatId: string, roundId: string) => {
        const currentTime = await DataService.getCurrentTime(chatId)

        if (currentTime.day < 6) {
          return {
            shouldSend: false,
            guidance: '课程预约还未开始'
          }
        }

        return {
          shouldSend: true,
          guidance: ''
        }
      },
    },
    {
      fileName: '第二课福利',
      fileId: '303369',
      condition: async (chatId: string, roundId: string) => {
        const isAfterCourse = await DataService.isInCourseTimeLine(chatId, 'afterCourse', 2)

        if (!isAfterCourse) {
          return {
            shouldSend: false,
            guidance: '课程还未结束'
          }
        }

        return {
          shouldSend: true,
          guidance: ''
        }
      },
    },
    {
      fileName: '第三课福利',
      fileId: '303369',
      condition: async (chatId: string, roundId: string) => {
        const isAfterCourse = await DataService.isInCourseTimeLine(chatId, 'afterCourse', 3)

        if (!isAfterCourse) {
          return {
            shouldSend: false,
            guidance: '课程还未结束'
          }
        }

        return {
          shouldSend: true,
          guidance: ''
        }
      },
    },
    {
      fileName: '第四课福利',
      fileId: '303369',
      condition: async (chatId: string, roundId: string) => {
        const isAfterCourse = await DataService.isInCourseTimeLine(chatId, 'afterCourse', 4)

        if (!isAfterCourse) {
          return {
            shouldSend: false,
            guidance: '课程还未结束'
          }
        }

        return {
          shouldSend: true,
          guidance: ''
        }
      },
    },
  ]


  public static async send(chatId: string, roundId: string): Promise<ActionInfo> {

    const fileNames = await SendFileAction.classifier(roundId)
    const fileList: ISendFile[] = []
    let guidance = ''


    for (const name of fileNames) {
      const file = this.fileList.find((file) => file.fileName === name)
      if (file && file.condition) {
        const res = await file.condition(chatId, roundId)
        if (res.shouldSend) {
          fileList.push(file)
        }
        else {
          guidance += `${res.guidance}\n`
        }
      }


      return {
        guidance: guidance,
        callback: async () => {
          await SendFileAction.sendFile(chatId, fileList)
        },
      }

    }

    return {
      guidance: '',
    }

  }



  private static async sendFile(chatId: string, file: ISendFile[]) {

  }






  private static async classifier(roundId: string) {

    const fileNameList = Object.keys(this.fileList).join('，')
    const promptTemplate = PromptTemplate.fromTemplate(this.getPrompt())
    const prompt = await promptTemplate.format({ fileNameList })

    const response = await LLM.predict(prompt, { meta:{ round_id:roundId }, responseJSON: true, })

    const jsonRes = JSON.parse(response)

    return jsonRes.answer as string[]

  }



  private static getPrompt() {
    const prompt = `# 发送资料
- 请根据当前时间和近两轮与客户交互中的信息，判断客户需要的资料，并从以下列表中选择对应的资料输出：【{fileNameList}】，如果没有找到对应资料，就输出【其他资料】

## 课程信息：
- 第一课：19:20到20:44
- 第二课：19:20到20:50
- 第三课：19:20到20:53
- 第四课：19:20到20:55
- 第五课：19:20到20:54
- 第六课：19:20到20:52


## 要求：
- 根据当前时间，客户的今日课程与对话记录综合分析客户需要的资料，可以同时输出多个资料
- 请主要关注客户的需求，而不是${Config.setting.AGENT_NAME}说的内容，历史已经发送过的资料不要重复发送
- 如果课程已结束，则可以发送任何客户需要的资料
- 请不要胡乱判断资料，找不到对应资料，或者找到资料了不满足时间等条件，都输出【其他资料】

# 以JSON格式输出
{{
    "reason":"说明判断的依据",
    "answer":[]
}}
`
    return prompt
  }


}