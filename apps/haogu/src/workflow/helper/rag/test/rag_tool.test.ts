import { ReasoningRagTool } from '../reasoning_rag_tool'
import { DataService } from '../../getter/get_data'
import { query } from 'express'


describe('ragToolTest', () => {
  it('testGeneralSearch', async () => {

    DataService.getCurrentTime = (chatId) => {
      return Promise.resolve({
        is_course_day: false,
        day: 8,
        time: '08:00:00',
      })
    }

    const query = '股票交易中A、B、C，三点和一二三高，单踩双踩分别是什么意思？'
    const tool = await ReasoningRagTool.getToolByKey(ReasoningRagTool.GeneralSearch)
    const res = await tool?.execute({
      strategy: '',
      chatId: '1',
      roundId: '1',
      searchKey: query
    })

    console.log(res)
  }, 60000)


  it('testTransactionSystemKnowledgeExtract', async () => {

    const query = '股票交易中A、B、C，三点和一二三高，单踩双踩分别是什么意思？'
    const tool = await ReasoningRagTool.getToolByKey(ReasoningRagTool.GeneralSearch)
    const res = await tool?.execute({
      strategy: '',
      chatId: '1',
      roundId: '1',
      searchKey: query
    })



  }, 9e8)
})
