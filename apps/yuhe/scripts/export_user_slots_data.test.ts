import { writeFileSync } from 'node:fs'
import { contentWithFrequency } from 'service/local_cache/type'
import { UserSlots } from 'service/user_slots/extract_user_slots'
import { PrismaMongoClient } from '../database/prisma'
import { DataService } from '../helper/getter/get_data'
import dayjs from 'dayjs'

describe('导出客户画像', () => {
  jest.setTimeout(60000000)
  test('导出第x期客户画像', async() => {
    const startCourseNo = 20250501
    const endCourseNo = 20250512
    const mongoClient = PrismaMongoClient.getInstance()
    const users = await mongoClient.chat.findMany({ where:{ course_no:{ gte:startCourseNo, lte:endCourseNo } } })
    let text = '昵称,性别,年龄,行业,店面类型,是否抖音在做,年营业额,所在城市\n'
    for (const user of users) {
      const name = user.contact.wx_name
      const userSlots = UserSlots.fromRecord(user.chat_state.userSlots as Record<string, contentWithFrequency>)
      const sex = userSlots.getSubTopicContent('基本信息', '年龄')
      const age = userSlots.getSubTopicContent('基本信息', '性别')
      const industry = userSlots.getSubTopicContent('基本信息', '行业类目')
      const revenue = userSlots.getSubTopicContent('基本信息', '年营业额')
      const shopType = userSlots.getSubTopicContent('基本信息', '店面类别')
      const isDouYinWork = userSlots.getSubTopicContent('抖音运营状态', '是否抖音在做')
      const city = userSlots.getSubTopicContent('基本信息', '所在城市')
      text += `${name},${sex},${age},${industry},${shopType},${isDouYinWork},${revenue},${city}\n`
    }
    writeFileSync('./info.csv', text)
  })

  test('导出第x期流量博士客户画像', async() => {
    const startCourseNo = 20250804
    const endCourseNo = 20250808
    const mongoClient = PrismaMongoClient.getInstance()

    // 指定的_id包含的字符串列表
    const targetIdParts = [
      '1688854467774270',
      '1688854707746016',
      '1688854952697121',
      '1688856296731952',
      '1688856090660659'
    ]

    const users = await mongoClient.chat.findMany({
      where: {
        course_no: { gte: startCourseNo, lte: endCourseNo },
        OR: targetIdParts.map((idPart) => ({
          id: { contains: idPart }
        }))
      }
    })
    let text = '昵称,性别,年龄,行业,店面类型,是否抖音在做,年营业额,所在城市\n'
    for (const user of users) {
      const name = user.contact.wx_name
      const userSlots = UserSlots.fromRecord(user.chat_state.userSlots as Record<string, contentWithFrequency>)
      const sex = userSlots.getSubTopicContent('基本信息', '年龄')
      const age = userSlots.getSubTopicContent('基本信息', '性别')
      const industry = userSlots.getSubTopicContent('基本信息', '行业类目')
      const revenue = userSlots.getSubTopicContent('基本信息', '年营业额')
      const shopType = userSlots.getSubTopicContent('基本信息', '店面类别')
      const isDouYinWork = userSlots.getSubTopicContent('抖音运营状态', '是否抖音在做')
      const city = userSlots.getSubTopicContent('基本信息', '所在城市')
      text += `${name},${sex},${age},${industry},${shopType},${isDouYinWork},${revenue},${city}\n`
    }
    writeFileSync('./info.csv', text)
  })

  test('统计day0或day1完课后不再理会AI的客户数量', async() => {
    const mongoClient = PrismaMongoClient.getInstance()

    // 分别定义两个课程期数范围进行对比
    const courseRange1 = { name: '0804-0808期', start: 20250804, end: 20250808 }
    const courseRange2 = { name: '0822-0823期', start: 20250822, end: 20250823 }

    // 指定的chat_id包含的字符串列表
    const targetIdParts = [
      '1688854467774270',
      '1688854707746016',
      '1688854952697121',
      '1688856296731952',
      '1688856090660659'
    ]

    // 分别查询两个期数范围的用户
    const users1 = await mongoClient.chat.findMany({
      where: {
        AND: [
          { course_no: { gte: courseRange1.start, lte: courseRange1.end } },
          {
            OR: targetIdParts.map((idPart) => ({
              id: { contains: idPart }
            }))
          }
        ]
      }
    })

    const users2 = await mongoClient.chat.findMany({
      where: {
        AND: [
          { course_no: { gte: courseRange2.start, lte: courseRange2.end } },
          {
            OR: targetIdParts.map((idPart) => ({
              id: { contains: idPart }
            }))
          }
        ]
      }
    })

    console.log(`${courseRange1.name}找到用户: ${users1.length}个`)
    console.log(`${courseRange2.name}找到用户: ${users2.length}个`)
    console.log(`总用户数: ${users1.length + users2.length}个`)

    // 分析函数
    async function analyzeUserGroup(users: any[], groupName: string) {
      console.log(`\n=== 开始分析${groupName} ===`)

      // 并行处理用户数据，每批处理20个用户
      const batchSize = 20
      const batches = []
      for (let i = 0; i < users.length; i += batchSize) {
        batches.push(users.slice(i, i + batchSize))
      }

      let day0InteractionThenSilent = 0  // day0有互动但后续不再理会AI
      let day1CompletedThenSilent = 0    // day1完课后不再理会AI
      let totalAnalyzed = 0

      console.log(`将分 ${batches.length} 批处理，每批 ${batchSize} 个用户`)

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex]
        console.log(`\n处理第 ${batchIndex + 1}/${batches.length} 批，包含 ${batch.length} 个用户`)

        // 并行处理当前批次的所有用户
        const batchResults = await Promise.all(
          batch.map(async (user, userIndex) => {
            const globalIndex = batchIndex * batchSize + userIndex + 1
            try {
              console.log(`  分析用户 ${globalIndex}/${users.length}: ${user.contact.wx_name} (${user.id})`)

              // 获取课程开始时间
              const courseStartTime = getCourseStartTime(user.course_no!)

              // 计算day0和day1的结束时间点
              const day0EndTime = new Date(courseStartTime.getTime() - 24 * 60 * 60 * 1000 + 23 * 60 * 60 * 1000 + 59 * 60 * 1000 + 59 * 1000) // day0 23:59:59
              const day1EndTime = new Date(courseStartTime.getTime() + 23 * 60 * 60 * 1000 + 59 * 60 * 1000 + 59 * 1000) // day1 23:59:59

              // 并行检查所有条件
              const [hasDay0Interaction, isDay1Completed] = await Promise.all([
                // 检查day0是否有互动
                hasAIInteractionBetween(mongoClient, user.id,
                  new Date(courseStartTime.getTime() - 24 * 60 * 60 * 1000), // day0开始
                  day0EndTime), // day0结束
                // 检查是否完成了day1课程
                checkDay1CompletionFromState(user)
              ])

              let day0Silent = false
              let day1Silent = false

              // 并行检查后续互动情况
              const interactionChecks = []
              if (hasDay0Interaction) {
                interactionChecks.push(
                  hasAIInteractionAfter(mongoClient, user.id, day0EndTime).then((hasInteraction) => {
                    if (!hasInteraction) {
                      day0Silent = true
                      console.log('    - Day0有互动但后续不再理会AI')
                    }
                  })
                )
              }

              if (isDay1Completed) {
                interactionChecks.push(
                  hasAIInteractionAfter(mongoClient, user.id, day1EndTime).then((hasInteraction) => {
                    if (!hasInteraction) {
                      day1Silent = true
                      console.log('    - Day1完课后不再理会AI')
                    }
                  })
                )
              }

              await Promise.all(interactionChecks)

              return {
                analyzed: true,
                day0Silent,
                day1Silent,
                courseNo: user.course_no
              }

            } catch (error) {
              console.error(`  分析用户 ${globalIndex} ${user.contact.wx_name} 时出错:`, error)
              return {
                analyzed: false,
                day0Silent: false,
                day1Silent: false,
                courseNo: user.course_no
              }
            }
          })
        )

        // 汇总当前批次的结果
        for (const result of batchResults) {
          if (result.analyzed) {
            totalAnalyzed++
            if (result.day0Silent) day0InteractionThenSilent++
            if (result.day1Silent) day1CompletedThenSilent++
          }
        }

        console.log(`第 ${batchIndex + 1} 批处理完成，当前统计: Day0=${day0InteractionThenSilent}, Day1=${day1CompletedThenSilent}`)
      }

      return {
        groupName,
        totalUsers: users.length,
        totalAnalyzed,
        day0InteractionThenSilent,
        day1CompletedThenSilent,
        day0SilentRate: users.length > 0 ? (day0InteractionThenSilent / users.length * 100).toFixed(2) : '0.00',
        day1SilentRate: users.length > 0 ? (day1CompletedThenSilent / users.length * 100).toFixed(2) : '0.00'
      }
    }

    // 分别分析两个期数范围
    const result1 = await analyzeUserGroup(users1, courseRange1.name)
    const result2 = await analyzeUserGroup(users2, courseRange2.name)

    console.log('\n=== 对比统计结果 ===')
    console.log(`\n${result1.groupName}:`)
    console.log(`  总用户数: ${result1.totalUsers}`)
    console.log(`  成功分析用户数: ${result1.totalAnalyzed}`)
    console.log(`  Day0有互动但后续不再理会AI: ${result1.day0InteractionThenSilent}人 (${result1.day0SilentRate}%)`)
    console.log(`  Day1完课后不再理会AI: ${result1.day1CompletedThenSilent}人 (${result1.day1SilentRate}%)`)

    console.log(`\n${result2.groupName}:`)
    console.log(`  总用户数: ${result2.totalUsers}`)
    console.log(`  成功分析用户数: ${result2.totalAnalyzed}`)
    console.log(`  Day0有互动但后续不再理会AI: ${result2.day0InteractionThenSilent}人 (${result2.day0SilentRate}%)`)
    console.log(`  Day1完课后不再理会AI: ${result2.day1CompletedThenSilent}人 (${result2.day1SilentRate}%)`)

    console.log(`\n=== 对比分析 ===`)
    const day0Diff = parseFloat(result1.day0SilentRate) - parseFloat(result2.day0SilentRate)
    const day1Diff = parseFloat(result1.day1SilentRate) - parseFloat(result2.day1SilentRate)
    console.log(`Day0沉默率差异: ${day0Diff > 0 ? '+' : ''}${day0Diff.toFixed(2)}% (${result1.groupName}相比${result2.groupName})`)
    console.log(`Day1沉默率差异: ${day1Diff > 0 ? '+' : ''}${day1Diff.toFixed(2)}% (${result1.groupName}相比${result2.groupName})`)

    // 保存对比结果到文件
    const resultText = `课程期数对比分析结果
${result1.groupName}
总用户数,${result1.totalUsers}
成功分析用户数,${result1.totalAnalyzed}
Day0有互动但后续不再理会AI,${result1.day0InteractionThenSilent},${result1.day0SilentRate}%
Day1完课后不再理会AI,${result1.day1CompletedThenSilent},${result1.day1SilentRate}%

${result2.groupName}
总用户数,${result2.totalUsers}
成功分析用户数,${result2.totalAnalyzed}
Day0有互动但后续不再理会AI,${result2.day0InteractionThenSilent},${result2.day0SilentRate}%
Day1完课后不再理会AI,${result2.day1CompletedThenSilent},${result2.day1SilentRate}%

对比分析
Day0沉默率差异,${day0Diff.toFixed(2)}%
Day1沉默率差异,${day1Diff.toFixed(2)}%`
    writeFileSync('./course_comparison_analysis.csv', resultText)
  })

  test('简单测试：验证查询功能', async() => {
    const mongoClient = PrismaMongoClient.getInstance()

    // 测试查询是否正常工作
    const testUsers = await mongoClient.chat.findMany({
      where: {
        course_no: { gte: 20250804, lte: 20250808 }
      },
      take: 5 // 只取5个用户进行测试
    })

    console.log(`测试查询到 ${testUsers.length} 个用户`)

    for (const user of testUsers) {
      console.log(`用户: ${user.contact.wx_name}, ID: ${user.id}, 课程期数: ${user.course_no}`)

      // 测试课程开始时间计算
      const courseStartTime = getCourseStartTime(user.course_no!)
      console.log(`  课程开始时间: ${courseStartTime}`)

      // 测试day1完课检查（从chat_state检查）
      try {
        const isDay1Completed = await checkDay1CompletionFromState(user)
        console.log(`  Day1完课状态（从状态检查）: ${isDay1Completed}`)
      } catch (error) {
        console.log(`  Day1完课检查失败: ${error}`)
      }
    }
  })
})

// 辅助函数：获取课程开始时间
function getCourseStartTime(courseNo: number): Date {
  return dayjs(courseNo.toString(10))
    .add(1, 'day')
    .hour(18)
    .minute(50)
    .second(0)
    .toDate()
}

// 辅助函数：检查是否完成课程（避免外部API调用）
async function isCompletedCourse(chatId: string, day: number): Promise<boolean> {
  try {
    return await DataService.isCompletedCourse(chatId, day)
  } catch (error) {
    console.error(`检查用户 ${chatId} day${day} 完课状态时出错:`, error)
    return false
  }
}

// 辅助函数：直接从chat_state检查day1完课状态
async function checkDay1CompletionFromState(user: any): Promise<boolean> {
  try {
    // 检查chat_state中是否有day1完课的标志
    const chatState = user.chat_state
    if (!chatState || !chatState.state) {
      return false
    }

    // 可能的day1完课标志
    const day1Flags = [
      'is_complete_course_day1',
      'is_complete_day1_course',
      'is_attend_course_day1'
    ]

    return day1Flags.some((flag) => chatState.state[flag] === true)
  } catch (error) {
    console.error(`检查用户 ${user.id} day1完课状态时出错:`, error)
    return false
  }
}

// 辅助函数：检查指定时间段内是否有AI互动
async function hasAIInteractionBetween(mongoClient: any, chatId: string, startTime: Date, endTime: Date): Promise<boolean> {
  try {
    // 查询指定时间段内的聊天记录
    const chatHistory = await mongoClient.chat_history.findMany({
      where: {
        chat_id: chatId,
        created_at: {
          gte: startTime,
          lte: endTime
        },
        role: 'user' // 只查看用户发送的消息
      },
      take: 1 // 只需要知道是否存在，所以只取一条
    })

    return chatHistory.length > 0
  } catch (error) {
    console.error(`检查用户 ${chatId} 在 ${startTime} 到 ${endTime} 期间的AI互动时出错:`, error)
    return false
  }
}

// 辅助函数：检查指定时间后是否还有与AI的互动
async function hasAIInteractionAfter(mongoClient: any, chatId: string, afterTime: Date): Promise<boolean> {
  try {
    // 查询指定时间后的聊天记录
    const chatHistory = await mongoClient.chat_history.findMany({
      where: {
        chat_id: chatId,
        created_at: {
          gt: afterTime
        },
        role: 'user' // 只查看用户发送的消息
      },
      orderBy: {
        created_at: 'asc'
      },
      take: 1 // 只需要知道是否存在，所以只取一条
    })

    return chatHistory.length > 0
  } catch (error) {
    console.error(`检查用户 ${chatId} 在 ${afterTime} 后的AI互动时出错:`, error)
    return false
  }
}