import { writeFileSync } from 'node:fs'
import { contentWithFrequency } from 'service/local_cache/type'
import { UserSlots } from 'service/user_slots/extract_user_slots'
import { PrismaMongoClient } from '../database/prisma'
import { DataService } from '../helper/getter/get_data'
import dayjs from 'dayjs'

describe('导出客户画像', () => {
  jest.setTimeout(60000000)
  test('导出第x期客户画像', async() => {
    const startCourseNo = 20250501
    const endCourseNo = 20250512
    const mongoClient = PrismaMongoClient.getInstance()
    const users = await mongoClient.chat.findMany({ where:{ course_no:{ gte:startCourseNo, lte:endCourseNo } } })
    let text = '昵称,性别,年龄,行业,店面类型,是否抖音在做,年营业额,所在城市\n'
    for (const user of users) {
      const name = user.contact.wx_name
      const userSlots = UserSlots.fromRecord(user.chat_state.userSlots as Record<string, contentWithFrequency>)
      const sex = userSlots.getSubTopicContent('基本信息', '年龄')
      const age = userSlots.getSubTopicContent('基本信息', '性别')
      const industry = userSlots.getSubTopicContent('基本信息', '行业类目')
      const revenue = userSlots.getSubTopicContent('基本信息', '年营业额')
      const shopType = userSlots.getSubTopicContent('基本信息', '店面类别')
      const isDouYinWork = userSlots.getSubTopicContent('抖音运营状态', '是否抖音在做')
      const city = userSlots.getSubTopicContent('基本信息', '所在城市')
      text += `${name},${sex},${age},${industry},${shopType},${isDouYinWork},${revenue},${city}\n`
    }
    writeFileSync('./info.csv', text)
  })

  test('导出第x期流量博士客户画像', async() => {
    const startCourseNo = 20250804
    const endCourseNo = 20250808
    const mongoClient = PrismaMongoClient.getInstance()

    // 指定的_id包含的字符串列表
    const targetIdParts = [
      '1688854467774270',
      '1688854707746016',
      '1688854952697121',
      '1688856296731952',
      '1688856090660659'
    ]

    const users = await mongoClient.chat.findMany({
      where: {
        course_no: { gte: startCourseNo, lte: endCourseNo },
        OR: targetIdParts.map((idPart) => ({
          id: { contains: idPart }
        }))
      }
    })
    let text = '昵称,性别,年龄,行业,店面类型,是否抖音在做,年营业额,所在城市\n'
    for (const user of users) {
      const name = user.contact.wx_name
      const userSlots = UserSlots.fromRecord(user.chat_state.userSlots as Record<string, contentWithFrequency>)
      const sex = userSlots.getSubTopicContent('基本信息', '年龄')
      const age = userSlots.getSubTopicContent('基本信息', '性别')
      const industry = userSlots.getSubTopicContent('基本信息', '行业类目')
      const revenue = userSlots.getSubTopicContent('基本信息', '年营业额')
      const shopType = userSlots.getSubTopicContent('基本信息', '店面类别')
      const isDouYinWork = userSlots.getSubTopicContent('抖音运营状态', '是否抖音在做')
      const city = userSlots.getSubTopicContent('基本信息', '所在城市')
      text += `${name},${sex},${age},${industry},${shopType},${isDouYinWork},${revenue},${city}\n`
    }
    writeFileSync('./info.csv', text)
  })

  test('统计day0或day1完课后不再理会AI的客户数量', async() => {
    const mongoClient = PrismaMongoClient.getInstance()

    // 指定的课程期数范围
    const courseNoRanges = [
      { start: 20250804, end: 20250808 },
      { start: 20250822, end: 20250823 }
    ]

    // 指定的chat_id包含的字符串列表
    const targetIdParts = [
      '1688854467774270',
      '1688854707746016',
      '1688854952697121',
      '1688856296731952',
      '1688856090660659'
    ]

    // 构建查询条件：课程期数在指定范围内，且chat_id包含targetIdParts中的任一字符串
    const users = await mongoClient.chat.findMany({
      where: {
        AND: [
          {
            OR: courseNoRanges.map(range => ({
              course_no: { gte: range.start, lte: range.end }
            }))
          },
          {
            OR: targetIdParts.map((idPart) => ({
              id: { contains: idPart }
            }))
          }
        ]
      }
    })

    console.log(`找到符合条件的用户总数: ${users.length}`)

    let day0CompletedThenSilent = 0
    let day1CompletedThenSilent = 0
    let day0OrDay1CompletedThenSilent = 0
    let totalAnalyzed = 0

    for (const user of users) {
      try {
        totalAnalyzed++
        console.log(`分析用户 ${totalAnalyzed}/${users.length}: ${user.contact.wx_name} (${user.id})`)

        // 获取课程开始时间
        const courseStartTime = getCourseStartTime(user.course_no!)

        // 计算day0和day1的结束时间点
        const day0EndTime = new Date(courseStartTime.getTime() - 24 * 60 * 60 * 1000 + 23 * 60 * 60 * 1000 + 59 * 60 * 1000 + 59 * 1000) // day0 23:59:59
        const day1EndTime = new Date(courseStartTime.getTime() + 23 * 60 * 60 * 1000 + 59 * 60 * 1000 + 59 * 1000) // day1 23:59:59

        // 检查是否完成了day1课程（yuhe项目不支持day0完课检查）
        const isDay1Completed = await isCompletedCourse(user.id, 1)

        // 检查day0完课状态（通过chat_state中的标志）
        const isDay0Completed = await checkDay0Completion(user)

        let userCompletedThenSilent = false

        if (isDay0Completed) {
          // 检查day0完课后是否还有与AI的互动
          const hasInteractionAfterDay0 = await hasAIInteractionAfter(mongoClient, user.id, day0EndTime)
          if (!hasInteractionAfterDay0) {
            day0CompletedThenSilent++
            userCompletedThenSilent = true
            console.log(`  - Day0完课后不再理会AI`)
          }
        }

        if (isDay1Completed) {
          // 检查day1完课后是否还有与AI的互动
          const hasInteractionAfterDay1 = await hasAIInteractionAfter(mongoClient, user.id, day1EndTime)
          if (!hasInteractionAfterDay1) {
            day1CompletedThenSilent++
            userCompletedThenSilent = true
            console.log(`  - Day1完课后不再理会AI`)
          }
        }

        if (userCompletedThenSilent) {
          day0OrDay1CompletedThenSilent++
        }

      } catch (error) {
        console.error(`分析用户 ${user.contact.wx_name} 时出错:`, error)
      }
    }

    console.log('\n=== 统计结果 ===')
    console.log(`总用户数: ${users.length}`)
    console.log(`成功分析用户数: ${totalAnalyzed}`)
    console.log(`Day0完课后不再理会AI的用户数: ${day0CompletedThenSilent}`)
    console.log(`Day1完课后不再理会AI的用户数: ${day1CompletedThenSilent}`)
    console.log(`Day0或Day1完课后不再理会AI的用户数: ${day0OrDay1CompletedThenSilent}`)

    // 保存结果到文件
    const resultText = `统计结果\n总用户数,${users.length}\n成功分析用户数,${totalAnalyzed}\nDay0完课后不再理会AI的用户数,${day0CompletedThenSilent}\nDay1完课后不再理会AI的用户数,${day1CompletedThenSilent}\nDay0或Day1完课后不再理会AI的用户数,${day0OrDay1CompletedThenSilent}`
    writeFileSync('./ai_interaction_analysis.csv', resultText)
  })
})

// 辅助函数：获取课程开始时间
function getCourseStartTime(courseNo: number): Date {
  return dayjs(courseNo.toString(10))
    .add(1, 'day')
    .hour(18)
    .minute(50)
    .second(0)
    .toDate()
}

// 辅助函数：检查是否完成课程
async function isCompletedCourse(chatId: string, day: number): Promise<boolean> {
  try {
    return await DataService.isCompletedCourse(chatId, day)
  } catch (error) {
    console.error(`检查用户 ${chatId} day${day} 完课状态时出错:`, error)
    return false
  }
}

// 辅助函数：检查day0完课状态（通过chat_state检查）
async function checkDay0Completion(user: any): Promise<boolean> {
  try {
    // 检查chat_state中是否有day0完课的标志
    const chatState = user.chat_state
    if (!chatState || !chatState.state) {
      return false
    }

    // 可能的day0完课标志
    const day0Flags = [
      'is_complete_course_day0',
      'is_complete_pre_course',
      'is_attend_course_day0'
    ]

    return day0Flags.some(flag => chatState.state[flag] === true)
  } catch (error) {
    console.error(`检查用户 ${user.id} day0完课状态时出错:`, error)
    return false
  }
}

// 辅助函数：检查指定时间后是否还有与AI的互动
async function hasAIInteractionAfter(mongoClient: any, chatId: string, afterTime: Date): Promise<boolean> {
  try {
    // 查询指定时间后的聊天记录
    const chatHistory = await mongoClient.chat_history.findMany({
      where: {
        chat_id: chatId,
        created_at: {
          gt: afterTime
        },
        role: 'user' // 只查看用户发送的消息
      },
      orderBy: {
        created_at: 'asc'
      },
      take: 1 // 只需要知道是否存在，所以只取一条
    })

    return chatHistory.length > 0
  } catch (error) {
    console.error(`检查用户 ${chatId} 在 ${afterTime} 后的AI互动时出错:`, error)
    return false
  }
}