import { Job, Worker } from 'bullmq'
import { Config } from 'config'
import logger from 'model/logger/logger'
import { RedisDB } from 'model/redis/redis'
import { AsyncLock } from 'model/lock/lock'
import { Action, ActionType, ContentAudio, ContentCustom, ContentDynamicPrompt, ContentFile, ContentImage, ContentLink, ContentTextPlain, ContentVideo, ContentVideoChannel, ContentVoice, ContentYCloudTemplate, getSopTopicConditionRedisKey, ITask, LinkSourceType, TextType, TopicCondition } from './visualized_sop_type'
import { getVisualizedSopQueueName, VisualizedSopTasks } from './visualized_sop_task_starter'
import { commonSleep, randomSleep, sleep } from 'lib/schedule/schedule'
import { ChatDB, IChat } from '../database/chat'
import { ChatHistoryService } from '../chat_history/chat_history'
import { MessageSender } from './common_sender'
import { sopActionProcessCounter, sopActionProcessErrorCounter, sopProcessCounter, sopProcessErrorCounter } from './prometheus'


export abstract class VisualizedSopProcessor {

  abstract getActionCustomMap():Record<string, (params:{chatId:string;userId:string;opt:HandleActionOption})=> Promise<void>>
  abstract getConditionJudgeMap():Record<string, ((params:{chatId:string;userId:string})=>Promise<boolean>)>
  abstract getLinkSourceVariableTagMap():Record<string, (params:{chatId:string;userId:string})=>Promise<string>>
  abstract getTextVariableMap():Record<string, (params:{chatId:string;userId:string})=> Promise<string>>
  abstract handleActionDynamicPrompt(chatId: string, userId: string, action:ContentDynamicPrompt, opt: HandleActionOption): Promise<void>

  private chatDBClient:ChatDB<IChat>
  private chatHistoryServiceClient:ChatHistoryService
  private enterPriseName:string
  private messageSender:MessageSender

  constructor(enterpriseName:string, chatDBClient:ChatDB<IChat>, chatHistoryServiceClient:ChatHistoryService, messageSender:MessageSender) {
    this.enterPriseName = enterpriseName
    this.chatDBClient = chatDBClient
    this.chatHistoryServiceClient = chatHistoryServiceClient
    this.messageSender = messageSender
  }

  public start() {
    if (!Config.setting.wechatConfig?.id) {
      throw ('获取wx bot id错误')
    }
    new Worker(getVisualizedSopQueueName(this.enterPriseName, Config.setting.wechatConfig.id), async (job: Job<ITask>) => {
      try {
        sopProcessCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.id }).inc(1)
        await this.generalProcess(this.enterPriseName, job)
      } catch (e) {
        sopProcessErrorCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.id }).inc(1)
        logger.error(`任务执行出错: ${job.name} ${job.data} ${e}`)
      }
    }, { connection: RedisDB.getInstance(),
      lockDuration: 60 * 1000,
      concurrency: 10,
      removeOnComplete: {
        age: 3600, // keep up to 1 hour
        count: 1000, // keep up to 1000 jobs
      },
      removeOnFail: {
        age: 24 * 3600, // keep up to 24 hours
      }
    })
  }

  public async generalProcess(enterPriseName:string, job: Job<ITask>) {
    if (job.opts.delay && job.opts.delay < 0) {
      // logger.warn(`任务 ${job.name} 超时 ${-job.opts.delay} 毫秒，不进行处理`)
      return
    }
    // 加锁，防止在 主动回复中间插入消息
    const lock = new AsyncLock()
    try {

      await lock.acquire(job.data.chatId, async () => {

        const chat = await this.chatDBClient.getById(job.data.chatId)
        if ((!chat) || (chat && chat.is_stop_group_push)) {
          return
        }

        // 如果最后一条消息是非营销消息，时间在 1 分钟内，非闲聊节点，延迟进行消息发送。
        const isLastMsgWithin1Minute = await this.chatHistoryServiceClient.isLastMessageWithDuration(chat.id, 1, 'minute')
        if (isLastMsgWithin1Minute) {
          await sleep(60 * 1000)
        }

        const task = job.data
        await this.handleSopBySopId(enterPriseName, task.chatId, task.userId, task.name, task.force)
      }, { timeout: 60 * 1000 })
    } catch (e) {
      sopProcessErrorCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.id }).inc(1)
      logger.error(e)
    }
  }

  public async handleSopBySopId(enterPriseName:string, chatId:string, userId:string, sopId:string, force:boolean = false) {
    const sops = await VisualizedSopTasks.getTasks(enterPriseName, Config.setting.wechatConfig!.id)
    const filteredSop = sops.filter((item) => {
      if (item.id != sopId) return false
      return true
    })
    for (const sop of filteredSop) {
      // 将所有条件取出来一次性判断
      await this.handleSop(chatId, userId, sop, force)
    }
  }

  public async handleSop(chatId:string, userId:string, sop:Awaited<ReturnType<typeof VisualizedSopTasks.getTasks>>[number], force:boolean = false) {
    logger.log(`客户${chatId}执行sop${sop.id} ${sop.title} ${sop.week}周${sop.day}天${sop.time}`)
    const redisClient = RedisDB.getInstance()
    const topicConditions = JSON.parse(await redisClient.get(getSopTopicConditionRedisKey(this.enterPriseName, sop.tag, sop.topic)) ?? '[]') as TopicCondition[]
    const conditionFixedTypes = new Set<string>()
    const conditionDynamicTypes = new Set<string>()
    for (const condition of topicConditions) {
      conditionFixedTypes.add(condition.condition)
    }
    for (const { conditions } of sop.situations) {
      for (const condition of conditions) {
        if (condition.type == 'fixed') {
          conditionFixedTypes.add(condition.condition)
        } else {
          conditionDynamicTypes.add(condition.condition)
        }
      }
    }
    const conditionsFixed = [...conditionFixedTypes]
    const conditionsDynamic = [...conditionDynamicTypes]
    const conditionFixedMap = new Map<string, boolean>()
    const conditionDynamicMap = new Map<string, boolean>()
    for (const condition of conditionsFixed) {
      conditionFixedMap.set(condition, await this.judgeFixedCondition(chatId, userId, condition))
    }
    for (const condition of conditionsDynamic) {
      conditionDynamicMap.set(condition, await this.judgeDynamicCondition(chatId, userId, condition))
    }
    logger.log(`客户${chatId}执行sop${sop.id} 时候固定条件是 ${JSON.stringify(Object.fromEntries(conditionFixedMap))} 动态条件是${JSON.stringify(Object.fromEntries(conditionDynamicMap))}`)
    //TODO: 重构为及时判断及时退出的版本

    if (!this.judgeConditions(topicConditions.map((item) => ({ type:'fixed', condition:item.condition, isOrNotIs: item.isOrNotIs })), conditionFixedMap, conditionDynamicMap)) {
      return
    }

    for (const situation of sop.situations) {
      if (!this.judgeConditions(situation.conditions, conditionFixedMap, conditionDynamicMap)) continue
      for (const action of situation.action) {
        await this.handleAction(chatId, userId, action, { force:force, sop_id:sop.id })
        await commonSleep()
      }
    }
  }

  public async judgeFixedCondition(chatId:string, userId:string, condition:string):Promise<boolean> {
    const func = this.getConditionJudgeMap()[condition]
    if (!func) throw (`判断条件没有这个键${condition}`)
    return await func({ userId, chatId })
  }

  abstract judgeDynamicCondition(chatId:string, userId:string, condition:string): Promise<boolean>


  public judgeConditions(conditions:{
        isOrNotIs: boolean
        type: string
        condition: string
    }[], conditionFixedMap:Map<string, boolean>, conditionDynamicMap:Map<string, boolean>) {
    for (const condition of conditions) {
      let conditionMap = new Map<string, boolean>()
      if (condition.type == 'dynamic') {
        conditionMap = conditionDynamicMap
      } else {
        conditionMap = conditionFixedMap
      }
      if (!conditionMap.has(condition.condition)) {
        logger.log('没有这个条件')
        return false
      }
      if ((condition.isOrNotIs && !conditionMap.get(condition.condition)) || (!condition.isOrNotIs && conditionMap.get(condition.condition))) {
        return false
      }
    }
    return true
  }

  public async handleAction(chatId:string, userId:string, action:Action, opt:HandleActionOption = { force:false }) {
    sopActionProcessCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.id }).inc(1)
    try {
      if (action.type == ActionType.text) {
        await this.handleActionText(chatId, userId, action, opt)
      } else if (action.type == ActionType.image) {
        await this.handleActionImage(chatId, userId, action, opt)
      } else if (action.type == ActionType.video) {
        await this.handleActionVideo(chatId, userId, action, opt)
      } else if (action.type == ActionType.file) {
        await this.handleActionFile(chatId, userId, action, opt)
      } else if (action.type == ActionType.voice) {
        await this.handleActionVoice(chatId, userId, action, opt)
      } else if (action.type == ActionType.audio) {
        await this.handleActionAudio(chatId, userId, action, opt)
      } else if (action.type == ActionType.custom) {
        await this.handleActionCustom(chatId, userId, action, opt)
      } else if (action.type == ActionType.link) {
        await this.handleActionLink(chatId, userId, action, opt)
      } else if (action.type == ActionType.videoChannel) {
        await this.handleActionVideoChannel(chatId, userId, action, opt)
      } else if (action.type == ActionType.dynamicPrompt) {
        await randomSleep(0, 1000 * 60)
        await this.handleActionDynamicPrompt(chatId, userId, action, opt)
      } else if (action.type == ActionType.ycloudTemplate) {
        await this.handleActionYCloudTemplate(chatId, userId, action, opt)
      } else {
        throw ('unknow action type')
      }
    } catch (e) {
      sopActionProcessErrorCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.id }).inc(1)
      logger.error(`客户${chatId} sop的时候action出错${e}`)
    }
  }

  public async handleActionText(chatId:string, userId:string, action: ContentTextPlain, opt:HandleActionOption) {
    let text = ''
    for (const item of action.textList) {
      if (item.type == TextType.fixed) {
        text += item.text
      } else {
        text += await this.getTextVariable(chatId, userId, item.tag)
      }
    }
    await this.messageSender.sendText(chatId, {
      text: text,
      description: action.description
    }, {
      force:opt.force,
      sopId:opt.sop_id
    })
  }

  public async getTextVariable(chatId:string, userId:string, variableTag:string):Promise<string> {
    const func = this.getTextVariableMap()[variableTag]
    if (!func) throw (`字符串变量没有这个键${variableTag}`)
    return await func({ userId, chatId })
  }

  public async handleActionImage(chatId:string, userId:string, action: ContentImage, opt:HandleActionOption) {
    await this.messageSender.sendImage(chatId, {
      url: action.url,
      description: action.description
    }, {
      force:opt.force,
      sopId:opt.sop_id
    })
  }

  public async handleActionVideo(chatId:string, userId:string, action: ContentVideo, opt:HandleActionOption) {
    await this.messageSender.sendVideo(chatId, {
      url: action.url,
      description: action.description
    }, {
      force:opt.force,
      sopId:opt.sop_id
    })
  }

  public async handleActionVoice(chatId:string, userId:string, action: ContentVoice, opt:HandleActionOption) {
    await this.messageSender.sendWecomVoice(chatId, {
      url: action.url,
      duration: action.duration,
      description: action.description
    }, {
      force:opt.force,
      sopId:opt.sop_id
    })
  }

  public async handleActionAudio(chatId:string, useId:string, action: ContentAudio, opt:HandleActionOption) {
    await this.messageSender.sendAudio(chatId, {
      url:action.url,
      description:action.description,
    }, {
      force:opt.force,
      sopId:opt.sop_id
    })
  }

  public async handleActionFile(chatId:string, userId:string, action: ContentFile, opt:HandleActionOption) {
    await this.messageSender.sendFile(chatId, {
      url: action.url,
      filename: action.name,
      description: action.description
    }, {
      force:opt.force,
      sopId:opt.sop_id
    })
  }

  public async handleActionCustom(chatId:string, userId:string, action: ContentCustom, opt:HandleActionOption):Promise<void> {
    const func = this.getActionCustomMap()[action.tag]
    if (!func) throw (`自定义事件没有这个键${action.tag}`)
    return await func({ userId, chatId, opt })
  }

  public async handleActionLink(chatId:string, userId:string, action: ContentLink, opt:HandleActionOption) {
    let sourceLink = ''
    if (action.source.type == LinkSourceType.fixed) {
      sourceLink = action.source.url
    } else if (action.source.type == LinkSourceType.variable) {
      const getSourceLinkFunc = this.getLinkSourceVariableTagMap()[action.source.tag]
      if (!getSourceLinkFunc) throw (`自定义事件没有这个键${action.source.tag}`)
      sourceLink = await getSourceLinkFunc({ chatId, userId })
    } else {
      throw ('unknow link source type')
    }
    await this.messageSender.sendWecomCard(chatId, {
      sourceUrl: sourceLink,
      title: action.title,
      summary: action.summary,
      imageUrl: action.imageUrl,
      description: action.description
    }, {
      force:opt.force,
      sopId:opt.sop_id
    })
  }

  public async handleActionVideoChannel(chatId:string, userId:string, action:ContentVideoChannel, opt: HandleActionOption) {
    await this.messageSender.sendWecomVideoChannel(chatId, {
      avatarUrl: action.avatarUrl,
      coverUrl: action.coverUrl,
      wecomContentdescription: action.contentDescription,
      nickname: action.nickname,
      thumbUrl: action.thumbUrl,
      url: action.url,
      extras: action.extras,
      description: action.description
    }, {
      force:opt.force,
      sopId:opt.sop_id
    })
  }

  public async handleActionYCloudTemplate(chatId:string, userId:string, action:ContentYCloudTemplate, opt:HandleActionOption) {
    await this.messageSender.sendYCloudTemplate(chatId, {
      description: action.description,
      templateName: action.templateName,
      language: action.language,
      variable: await Promise.all(action.variable.map((item) => this.getTextVariable(chatId, userId, item))),
      header:action.header
    }, {
      force:opt.force,
      sopId:opt.sop_id
    })
  }
}


export interface HandleActionOption {
  force: boolean
  sop_id?:string
}

