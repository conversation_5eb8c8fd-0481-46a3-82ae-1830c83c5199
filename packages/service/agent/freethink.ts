import logger from 'model/logger/logger'
import { ChatHistoryService } from '../chat_history/chat_history'
import { EventTracker, IEventType } from 'model/logger/data_driven'
import { getPrompt } from './prompt'
import { IWorkflowState } from '../llm/state'
import { LLM } from 'lib/ai/llm/llm_model'

export class FreeThink {
  private chatHistoryServiceClient: ChatHistoryService
  private eventTrackClient: EventTracker
  constructor(chatHistoryServiceClient: ChatHistoryService, eventTrackClient: EventTracker) {
    this.chatHistoryServiceClient = chatHistoryServiceClient
    this.eventTrackClient = eventTrackClient
  }

  public async invoke(state: IWorkflowState, thinkPrompt: string, metaActions: string, customerBehavior: string, customerPortrait: string, temporalInformation: string) {
    const freeThinkPrompt = await getPrompt('free-think-pro')
    const dialogHistory = await this.chatHistoryServiceClient.getChatHistory(state.chat_id, 6, 18)
    await state.interruptHandler.interruptCheck()

    const output = await LLM.predict(
      freeThinkPrompt, {
        model: 'gpt-5',
        responseJSON: true,
        meta: {
          promptName: 'free_think',
          chat_id: state.chat_id,
          round_id: state.round_id,
        } }, {
        thinkPrompt: thinkPrompt,
        metaActions: metaActions,
        customerBehavior: customerBehavior,
        customerPortrait: customerPortrait,
        dialogHistory: dialogHistory,
        temporalInformation: temporalInformation,
      })
    let think: string = ''
    let action: string[] = []
    let strategy: string | string[] = '正常回复'
    let task: string[] = []
    let content: string = ''

    try {
      const parsedOutput = JSON.parse(output)
      think = parsedOutput.think
      action = parsedOutput.action
      strategy = parsedOutput.strategy
      if (Array.isArray(strategy)) {
        task = strategy.slice(2)
        strategy = strategy.slice(0, 2).join(' ')
      }
      content = parsedOutput.content
    } catch (error) {
      logger.error('FreeThink 解析 JSON 失败:', error)
    }

    this.eventTrackClient.track(state.chat_id, IEventType.FreeThink, {
      round_id: state.round_id,
      think: think,
      action: JSON.stringify(action),
      strategy: strategy,
      task: JSON.stringify(task),
      content: content
    })
    logger.debug({ chat_id: state.chat_id, round_id: state.round_id }, `think: ${think}\naction: ${JSON.stringify(action)}\nstrategy: ${strategy}`)
    return { think, action, strategy, content }
  }
}