import axios, { AxiosError, AxiosInstance } from 'axios'
import JSEncrypt from 'jsencrypt'
import logger from '../../logger/logger'
import type {
  QueryAiConversationStaffListRes,
  GetCustByStaffIdRes,
  GetCustomInfoRes,
  QueryChatPageRes,
  GetCustomerLabelRes,
  GetCustomerAILabelRes,
  MarkAILabelRes,
  SendTextMessageRes,
  SendImageMessageRes,
  SendEmotionMessageRes,
  SendVideoMessageRes,
  SendFileMessageRes,
  SendLinkMessageRes,
  SendMiniProgramMessageRes,
  ContentListRes,
  SendMediaMessageRes,
} from './type'

export class HaoguApi {
  private client:AxiosInstance
  private encrypt:JSEncrypt
  private maxBlockSize: number = 117
  constructor(baseUrl:string, secretKey:string) {
    const axiosClient = axios.create({
      baseURL: baseUrl,
      timeout: 10000,
    })
    this.encrypt = new JSEncrypt()
    this.encrypt.setPublicKey(secretKey)
    axiosClient.interceptors.request.use((config) => {
    // 在发送请求之前做些什么
      config.headers['Content-Type'] = 'application/json;charset=utf-8'
      if (!config.data) {
        config.data = {}
      }
      config.data['reqTime'] = new Date().getTime()
      const data = JSON.stringify(config.data)
      const encryptedData = this.encryptWithSegmentation(data)
      if (!encryptedData) {
        return Promise.reject(new Error('数据加密失败'))
      }
      config.data = {
        param_str: encryptedData
      }
      return config
    }, function (error) {
    // 对请求错误做些什么
      Promise.reject(error)
    })
    this.client = axiosClient
  }

  private encryptWithSegmentation(data: string): string | null {
    try {
      if (Buffer.byteLength(data, 'utf-8') <= this.maxBlockSize) {
        const encrypted = this.encrypt.encrypt(data)
        if (!encrypted) {
          logger.error('加密失败')
          return null
        }
        return encrypted
      }

      const encryptedBuffers: Buffer[] = []
      let currentPos = 0
      while (currentPos < data.length) {
        let segmentEnd = currentPos
        let segmentByteLength = 0
        while (segmentEnd < data.length && segmentByteLength < this.maxBlockSize) {
          const char = data[segmentEnd]
          const charByteLength = Buffer.byteLength(char, 'utf-8')
          if (segmentByteLength + charByteLength > this.maxBlockSize) {
            break
          }
          segmentByteLength += charByteLength
          segmentEnd++
        }
        if (segmentEnd === currentPos) {
          logger.error(`单个字符超出最大块大小: ${this.maxBlockSize}`)
          return null
        }
        const segment = data.substring(currentPos, segmentEnd)
        const encryptedBlock = this.encrypt.encrypt(segment)
        if (!encryptedBlock) {
          logger.error(`分段加密失败，位置: ${currentPos}`)
          return null
        }
        const encryptedBuffer = Buffer.from(encryptedBlock, 'base64')
        encryptedBuffers.push(encryptedBuffer)
        currentPos = segmentEnd
      }
      const combinedBuffer = Buffer.concat(encryptedBuffers)
      return combinedBuffer.toString('base64')
    } catch (e) {
      logger.error('加密处理异常', e)
      return null
    }
  }

  public async queryAiConversationStaffList(): Promise<QueryAiConversationStaffListRes | null> {
    const path = '/api/v1.0/ai/conversation/queryAiConversationStaffList'
    const payload = {}
    try {
      const response = await this.client.post<QueryAiConversationStaffListRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, e.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }

  public async getCustByStaffId(staffId: number | string, page: number, pageSize: number): Promise<GetCustByStaffIdRes | null> {
    const path = '/api/v1.0/ai/conversation/getCustByStaffId'
    const payload = { staffId, pageNo: page, pageSize }
    try {
      const response = await this.client.post<GetCustByStaffIdRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, e.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }

  public async getCustomInfo(page: number, pageSize: number, custUnifiedUserId: number | string): Promise<GetCustomInfoRes | null> {
    const path = '/api/v1.0/ai/conversation/getCustomInfo'
    const payload = { custUnifiedUserId, pageNo: page, pageSize }
    try {
      const response = await this.client.post<GetCustomInfoRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, e.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }

  public async queryChatPage(page: number, pageSize: number, staffId: number | string, startTime: number | undefined, endTime: number | undefined, custUnifiedUserId: number | string): Promise<QueryChatPageRes | null> {
    const path = '/api/v1.0/ai/conversation/queryChatPage'
    const payload = { current: page, size: pageSize, staffId, startTime, endTime, custUnifiedUserId }
    try {
      const response = await this.client.post<QueryChatPageRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, e.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }

  public async getCustomerLabel(staffId: number, custUnifiedUserId: number | string): Promise<GetCustomerLabelRes | null> {
    const path = '/api/v1.0/ai/conversation/label/getCustomerLabel'
    const payload = { staffId, custUnifiedUserId }
    console.log(payload)

    try {
      const response = await this.client.post<GetCustomerLabelRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, payload, e.response?.data,)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }

  public async getCustomerAILabel(staffId: number, custUnifiedUserIds: number[]): Promise<GetCustomerAILabelRes | null> {
    const path = '/api/v1.0/ai/conversation/label/getCustomerAILabel'
    const payload = { staffId, custUnifiedUserIds }
    try {
      const response = await this.client.post<GetCustomerAILabelRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, e.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }

  public async markAILabel(staffId: number, custUnifiedUserId: number, addLabel: string[], removeLabel: string[]): Promise<MarkAILabelRes | null> {
    const path = '/api/v1.0/ai/conversation/label/markAILabel'
    const payload = { staffId, custUnifiedUserId, addLabel, removeLabel }
    try {
      const response = await this.client.post<MarkAILabelRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, e.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }

  public async sendTextMessage(toolUserId: string, msgContent: string, conversationId: string, mockId: string): Promise<SendTextMessageRes | null> {
    const path = '/api/v1.0/ai/conversation/chat/sendTextMessage'
    const payload = { toolUserId, msgContent, conversationId, mockId }
    try {
      const response = await this.client.post<SendTextMessageRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, e.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }

  public async sendImageMessage(toolUserId: string, conversationId: string, mockId: string, fileUrl: string, fileSize: number, imageWidth: number, imageHeight: number, md5: string): Promise<SendImageMessageRes | null> {
    const path = '/api/v1.0/ai/conversation/chat/sendImageMessage'
    const payload = { toolUserId, conversationId, mockId, fileUrl, fileSize, imageWidth, imageHeight, md5 }
    try {
      const response = await this.client.post<SendImageMessageRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, e.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }

  public async sendEmotionMessage(toolUserId: string, conversationId: string, mockId: string, fileUrl: string, type: 1 | 2): Promise<SendEmotionMessageRes | null> {
    const path = '/api/v1.0/ai/conversation/chat/sendEmotionMessage'
    const payload = { toolUserId, conversationId, mockId, fileUrl, type }
    try {
      const response = await this.client.post<SendEmotionMessageRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, e.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }

  public async sendVideoMessage(toolUserId: string, conversationId: string, mockId: string, fileUrl: string, fileSize: number, imageWidth: number, imageHeight: number, md5: string): Promise<SendVideoMessageRes | null> {
    const path = '/api/v1.0/ai/conversation/chat/sendVideoMessage'
    const payload = { toolUserId, conversationId, mockId, fileUrl, fileSize, imageWidth, imageHeight, md5 }
    try {
      const response = await this.client.post<SendVideoMessageRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, e.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }

  public async sendFileMessage(toolUserId: string, conversationId: string, mockId: string, fileUrl: string, fileSize: number, md5: string): Promise<SendFileMessageRes | null> {
    const path = '/api/v1.0/ai/conversation/chat/sendFileMessage'
    const payload = { toolUserId, conversationId, mockId, fileUrl, fileSize, md5 }
    try {
      const response = await this.client.post<SendFileMessageRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, e.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }

  public async sendLinkMessage(toolUserId: string, conversationId: string, mockId: string, title: string, desc: string, url: string, imageUrl: string): Promise<SendLinkMessageRes | null> {
    const path = '/api/v1.0/ai/conversation/chat/sendLinkMessage'
    const payload = { toolUserId, conversationId, mockId, title, desc, url, imageUrl }
    try {
      const response = await this.client.post<SendLinkMessageRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, e.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }

  public async sendMiniProgramMessage(toolUserId: string, conversationId: string, mockId: string, mpvVideoId: number | string): Promise<SendMiniProgramMessageRes | null> {
    const path = '/api/v1.0/ai/conversation/chat/sendMiniProgramMessage'
    const payload = { toolUserId, conversationId, mockId, mpv_video_id: mpvVideoId }
    try {
      const response = await this.client.post<SendMiniProgramMessageRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, e.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }

  public async sendMediaMessage(toolUserId:string, conversationId:string, mockId:string, fileUrl:string, fileSourceUrl:string, fileTime:number, md5:string):Promise<SendMediaMessageRes | null> {
    const path = '/api/v1.0/ai/conversation/chat/sendMediaMessage'
    const payload = { toolUserId, conversationId, mockId, file_url:fileUrl, file_source_url:fileSourceUrl, file_time:fileTime, md5 }
    try {
      const response = await this.client.post<SendMediaMessageRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, e.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }

  /**
   * 查询素材
   * @param pageNo 默认为1
   * @param pageSize 默认为10
   * @param staffId 员工id
   * @param type 素材类型 1文章 2文件 7链接 10海报 13 多媒体 104视频号 300文本
   * @param mediaType 多媒体子类型（当 type=13 时） 1图片 2音频 3视频 4语音
   * @param shared 1企业内共享 0个人创建
   */
  public async contentList(pageNo = 1, pageSize = 10, staffId?: number | string, type?: number, mediaType?: number, shared?: number): Promise<ContentListRes | null> {
    const path = '/api/v1.0/ai/conversation/contentList'
    const payload = { pageNo, pageSize, staffId, type, mediaType, shared }
    try {
      const response = await this.client.post<ContentListRes>(path, payload)
      return response.data
    } catch (e) {
      if (e instanceof AxiosError) {
        logger.error(`请求接口${path}错误`, e.message, e.response?.data, payload)
      } else {
        logger.error(`请求接口${path}错误`, e)
      }
      return null
    }
  }
}